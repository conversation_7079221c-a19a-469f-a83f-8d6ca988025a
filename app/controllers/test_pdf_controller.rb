# frozen_string_literal: true

class TestPdfController < ApplicationController
  # Skip all authentication for development testing
  skip_before_action :authenticate_patient!, if: :is_frontend?
  skip_before_action :check_2fa, if: :is_frontend?

  def index
    # Simple index page with links to test the PDFs
  end

  def signature_page
    setup_test_data

    respond_to do |format|
      format.html do
        render template: 'admin/pdfs/secure_send_signature_page', layout: false
      end
      format.pdf do
        pdf_content = WickedPdf.new.pdf_from_string(
          render_to_string(
            template: 'admin/pdfs/secure_send_signature_page',
            layout: false
          ),
          page_size: 'Letter',
          margin: { top: 0, bottom: 0, left: 0, right: 0 },
          encoding: 'UTF-8',
          enable_local_file_access: true
        )

        send_data pdf_content,
                  filename: 'test_signature_page.pdf',
                  type: 'application/pdf',
                  disposition: 'inline'
      end
    end
  end

  def audit_trail
    setup_test_data

    respond_to do |format|
      format.html do
        render template: 'admin/pdfs/secure_send_audit_trail', layout: false
      end
      format.pdf do
        pdf_content = WickedPdf.new.pdf_from_string(
          render_to_string(
            template: 'admin/pdfs/secure_send_audit_trail',
            layout: false
          ),
          page_size: 'Letter',
          margin: { top: 20, bottom: 20, left: 20, right: 20 },
          encoding: 'UTF-8',
          enable_local_file_access: true
        )

        send_data pdf_content,
                  filename: 'test_audit_trail.pdf',
                  type: 'application/pdf',
                  disposition: 'inline'
      end
    end
  end

  def full_document
    setup_test_data

    service = SecureSend::PdfProcessingService.new(@signature_request)
    pdf_content = service.generate_signed_pdf_with_audit(
      html_content: sample_html_content
    )

    send_data pdf_content,
              filename: 'test_full_document.pdf',
              type: 'application/pdf',
              disposition: 'inline'
  end

  private

  def setup_test_data
    # Create or find test patient
    @patient = Patient.first || create_test_patient

    # Create or find test practice
    practice = Practice.first || create_test_practice

    # Create test document first
    @document = create_test_document

    # Create test signature request (needs document reference)
    @signature_request = create_test_signature_request

    # Set up test data for templates
    @signature_fields = [
      {
        id: 'patient_signature',
        type: 'signature',
        label: 'Patient Signature',
        merge_tag: '{{{signature:patient_signature}}}',
        position: 100,
        occurrence: 1
      },
      {
        id: 'patient_initials',
        type: 'initials',
        label: 'Patient Initials',
        merge_tag: '{{{signature:patient_initials}}}',
        position: 200,
        occurrence: 1
      },
      {
        id: 'signature_date',
        type: 'date',
        label: 'Date Signed',
        merge_tag: '{{{signature:signature_date}}}',
        position: 300,
        occurrence: 1
      }
    ]

    @merge_tags = {
      'patient_name' => { value: @patient.full_name, context: 'Patient name appears in the document header' },
      'practice_name' => { value: practice.name, context: 'Practice name for identification' },
      'document_date' => { value: Date.current.strftime('%d/%m/%Y'), context: 'Date the document was created' }
    }

    @background_image_url = 'data:image/svg+xml;base64,' + Base64.strict_encode64(
      '<svg xmlns="http://www.w3.org/2000/svg" width="100" height="100"><rect width="100" height="100" fill="#f0f0f0"/></svg>'
    )

    @default_avatar_url = 'data:image/svg+xml;base64,' + Base64.strict_encode64(
      '<svg xmlns="http://www.w3.org/2000/svg" width="95" height="95"><circle cx="47.5" cy="47.5" r="40" fill="#ddd"/><text x="47.5" y="52" text-anchor="middle" font-size="20" fill="#999">User</text></svg>'
    )
  end

  def create_test_patient
    Patient.create!(
      first_name: 'John',
      last_name: 'Doe',
      email: '<EMAIL>',
      phone: '+447123456789',
      date_of_birth: 30.years.ago.to_date
    )
  end

  def create_test_practice
    Practice.create!(
      name: 'Test Dental Practice',
      address_line_1: '123 Test Street',
      city: 'London',
      postcode: 'SW1A 1AA',
      phone: '+442071234567',
      email: '<EMAIL>'
    )
  end

  def create_test_document
    # Create a more complete mock document
    document_template = OpenStruct.new(
      text: sample_html_content,
      present?: true
    )

    OpenStruct.new(
      id: 12_345,
      title: 'Test Consent Form',
      document_template: document_template
    )
  end

  def create_test_signature_request
    # Create mock signature verifications
    verifications = [
      OpenStruct.new(
        verification_type: 'patient_pin',
        successful: true,
        signature_request: OpenStruct.new(witnessed_by: nil)
      ),
      OpenStruct.new(
        verification_type: '2fa',
        successful: true,
        signature_request: OpenStruct.new(witnessed_by: nil)
      )
    ]

    # Create mock signature image
    signature_image = OpenStruct.new(
      attached?: false,
      download: nil
    )

    OpenStruct.new(
      id: 67_890,
      patient: @patient,
      signable_document: @document,
      signature_type: 'typed',
      signed_name: 'John Doe',
      completed_at: 1.hour.ago,
      ip_address: '*************',
      device_type: 'Desktop',
      os_name: 'Windows',
      os_version: '11',
      browser_name: 'Chrome',
      browser_version: '120.0',
      sinch_fingerprint: 'test-fingerprint-123',
      date_fields: [Date.current.strftime('%d/%m/%Y')],
      signature_image: signature_image,
      signature_verifications: verifications,
      witnessed_by: nil
    )
  end

  def sample_html_content
    <<~HTML
      <!DOCTYPE html>
      <html>
      <head>
        <title>Test Document</title>
        <style>
          body { font-family: Arial, sans-serif; margin: 20px; }
          .header { text-align: center; margin-bottom: 30px; }
          .content { line-height: 1.6; }
          .signature-field { border: 1px dashed #ccc; padding: 10px; margin: 10px 0; }
        </style>
      </head>
      <body>
        <div class="header">
          <h1>Dental Consent Form</h1>
          <p>Patient: <span data-merge-tag="patient_name">#{@patient&.full_name || 'John Doe'}</span></p>
        </div>
      #{'  '}
        <div class="content">
          <p>I, the undersigned patient, acknowledge that I have been informed of the proposed dental treatment and understand the risks and benefits involved.</p>
      #{'    '}
          <p>By signing below, I give my informed consent for the treatment to proceed.</p>
      #{'    '}
          <div class="signature-field">
            <p>Patient Signature: {{{signature:patient_signature}}}</p>
          </div>
      #{'    '}
          <div class="signature-field">
            <p>Patient Initials: {{{signature:patient_initials}}}</p>
          </div>
      #{'    '}
          <div class="signature-field">
            <p>Date: {{{signature:signature_date}}}</p>
          </div>
        </div>
      </body>
      </html>
    HTML
  end
end
